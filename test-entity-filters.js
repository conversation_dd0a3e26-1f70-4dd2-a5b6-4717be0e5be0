#!/usr/bin/env node

/**
 * Simple test script to verify entity filter functionality
 * This script tests the conversion utility directly
 */

// Test the conversion utility directly
const ENTITY_TYPE_MAPPING = {
  'AI Tool': 'tool',
  'Course': 'course',
  'Job': 'job',
  'Hardware': 'hardware',
  'Event': 'event',
  'Agency': 'agency',
  'Software': 'software',
  'Research Paper': 'research_paper',
  'Podcast': 'podcast',
  'Community': 'community',
  'Grant': 'grant',
  'Newsletter': 'newsletter',
  'Book': 'book',
  'Platform': 'software',
  'API': 'tool',
  'Model': 'tool',
  'Dataset': 'tool',
  'Library': 'tool',
  'Service': 'tool',
  'Tool': 'tool',
};

function convertEntityTypeFilters(frontendFilters) {
  const backendFilters = {};

  for (const [displayName, filters] of Object.entries(frontendFilters)) {
    const backendKey = ENTITY_TYPE_MAPPING[displayName];
    if (backendKey && filters && Object.keys(filters).length > 0) {
      backendFilters[backendKey] = filters;
    }
  }

  return backendFilters;
}

const API_BASE_URL = 'https://ai-nav.onrender.com';

async function testEntityFilters() {
  console.log('🧪 Testing Entity Filter Conversion\n');

  // Test the conversion utility
  console.log('🔍 Testing convertEntityTypeFilters utility:');

  const testInput = {
    "AI Tool": {
      "technical_levels": ["BEGINNER"],
      "has_api": true
    }
  };

  console.log('Input (frontend):', JSON.stringify(testInput, null, 2));

  const converted = convertEntityTypeFilters(testInput);
  console.log('Output (backend):', JSON.stringify(converted, null, 2));

  const expectedOutput = {
    "tool": {
      "technical_levels": ["BEGINNER"],
      "has_api": true
    }
  };

  const isCorrect = JSON.stringify(converted) === JSON.stringify(expectedOutput);
  console.log('✅ Conversion working:', isCorrect);

  if (!isCorrect) {
    console.log('❌ Expected:', JSON.stringify(expectedOutput, null, 2));
    console.log('❌ Got:', JSON.stringify(converted, null, 2));
  }

  console.log('\n🌐 Testing API calls:');

  const testCases = [
    {
      name: 'Basic API Call (No Filters)',
      url: `${API_BASE_URL}/entities?page=1&limit=5`,
      expectSuccess: true
    },
    {
      name: 'Basic Filter (hasFreeTier) - Should Work',
      url: `${API_BASE_URL}/entities?page=1&limit=5&hasFreeTier=true`,
      expectSuccess: true
    },
    {
      name: 'Entity-Specific Filter (technical_levels) - Should Fail',
      url: `${API_BASE_URL}/entities?page=1&limit=5&technical_levels=BEGINNER`,
      expectSuccess: false,
      expectedError: 'property technical_levels should not exist'
    },
    {
      name: 'Entity Type Filters Object - Should Fail',
      url: `${API_BASE_URL}/entities?page=1&limit=5&entity_type_filters=${encodeURIComponent(JSON.stringify(expectedOutput))}`,
      expectSuccess: false,
      expectedError: 'property tool should not exist'
    }
  ];

  for (const testCase of testCases) {
    console.log(`🔍 Testing: ${testCase.name}`);
    console.log(`📤 URL: ${testCase.url}`);
    
    try {
      const response = await fetch(testCase.url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const responseText = await response.text();
      
      if (response.ok) {
        if (testCase.expectSuccess) {
          console.log(`✅ Success (${response.status})`);
          try {
            const json = JSON.parse(responseText);
            console.log(`📊 Results: ${json.data?.length || 0} entities found`);
          } catch {
            console.log('📊 Response received (non-JSON)');
          }
        } else {
          console.log(`⚠️  Unexpected success (expected failure)`);
        }
      } else {
        if (!testCase.expectSuccess) {
          console.log(`✅ Expected failure (${response.status})`);
          if (testCase.expectedError && responseText.includes(testCase.expectedError)) {
            console.log(`✅ Correct error message found`);
          } else {
            console.log(`⚠️  Different error than expected`);
          }
        } else {
          console.log(`❌ Unexpected error (${response.status}):`, responseText.substring(0, 200));
        }
      }
    } catch (error) {
      console.log(`❌ Network Error:`, error.message);
    }
    
    console.log('---\n');
  }

  console.log('🎯 Test Summary:');
  console.log('- Basic API calls should work');
  console.log('- Entity-specific filters should NOT be sent to backend');
  console.log('- Frontend should handle entity-specific filters locally');
  console.log('- No 400 errors should occur for basic filtering');
}

// Test the conversion directly
console.log('🧪 Testing Entity Filter Conversion\n');

const testInput = {
  "AI Tool": {
    "technical_levels": ["BEGINNER"],
    "has_api": true
  }
};

console.log('Input (frontend):', JSON.stringify(testInput, null, 2));

const converted = convertEntityTypeFilters(testInput);
console.log('Output (backend):', JSON.stringify(converted, null, 2));

const expectedOutput = {
  "tool": {
    "technical_levels": ["BEGINNER"],
    "has_api": true
  }
};

const isCorrect = JSON.stringify(converted) === JSON.stringify(expectedOutput);
console.log('✅ Conversion working:', isCorrect);

if (!isCorrect) {
  console.log('❌ Expected:', JSON.stringify(expectedOutput, null, 2));
  console.log('❌ Got:', JSON.stringify(converted, null, 2));
}

console.log('\n🌐 URL Encoding Test:');
console.log('Correct format URL encoded:', encodeURIComponent(JSON.stringify(expectedOutput)));
console.log('Wrong format URL encoded:', encodeURIComponent(JSON.stringify(testInput)));

// Run the tests if in a Node.js environment
if (typeof fetch !== 'undefined') {
  testEntityFilters().catch(console.error);
} else {
  console.log('\n⚠️ Skipping API tests (no fetch available)');
}
