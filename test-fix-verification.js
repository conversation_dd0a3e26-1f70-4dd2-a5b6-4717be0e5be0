#!/usr/bin/env node

/**
 * Verification script for entity filters fix
 * Tests that the 400 errors are resolved and basic filtering works
 */

const API_BASE_URL = 'https://ai-nav.onrender.com';

async function testFix() {
  console.log('🧪 Testing Entity Filters Fix\n');

  const testCases = [
    {
      name: '✅ Basic API Call',
      url: `${API_BASE_URL}/entities?page=1&limit=3`,
      expectSuccess: true,
      description: 'Should work without any issues'
    },
    {
      name: '✅ Basic Filter (hasFreeTier)',
      url: `${API_BASE_URL}/entities?page=1&limit=3&hasFreeTier=true`,
      expectSuccess: true,
      description: 'Basic filters should continue working'
    },
    {
      name: '✅ Multiple Basic Filters',
      url: `${API_BASE_URL}/entities?page=1&limit=3&hasFreeTier=true&apiAccess=true`,
      expectSuccess: true,
      description: 'Multiple basic filters should work'
    },
    {
      name: '❌ Entity-Specific Filter (expected to fail)',
      url: `${API_BASE_URL}/entities?page=1&limit=3&technical_levels=BEGINNER`,
      expectSuccess: false,
      description: 'Backend still rejects entity-specific filters (expected)',
      expectedError: 'technical_levels should not exist'
    },
    {
      name: '❌ Entity Type Filters Object (expected to fail)',
      url: `${API_BASE_URL}/entities?page=1&limit=3&entity_type_filters=${encodeURIComponent('{"tool":{"technical_levels":["BEGINNER"]}}')}`,
      expectSuccess: false,
      description: 'Backend still rejects entity_type_filters (expected)',
      expectedError: 'property tool should not exist'
    }
  ];

  let passedTests = 0;
  let totalTests = testCases.length;

  for (const testCase of testCases) {
    console.log(`🔍 ${testCase.name}`);
    console.log(`   ${testCase.description}`);
    
    try {
      const response = await fetch(testCase.url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const responseText = await response.text();
      
      if (response.ok) {
        if (testCase.expectSuccess) {
          console.log(`   ✅ PASS - Success (${response.status})`);
          try {
            const json = JSON.parse(responseText);
            console.log(`   📊 Found ${json.data?.length || 0} entities`);
          } catch {
            console.log(`   📊 Response received`);
          }
          passedTests++;
        } else {
          console.log(`   ❌ FAIL - Expected failure but got success`);
        }
      } else {
        if (!testCase.expectSuccess) {
          console.log(`   ✅ PASS - Expected failure (${response.status})`);
          if (testCase.expectedError && responseText.includes(testCase.expectedError)) {
            console.log(`   ✅ Correct error message confirmed`);
          }
          passedTests++;
        } else {
          console.log(`   ❌ FAIL - Unexpected error (${response.status})`);
          console.log(`   📄 Error: ${responseText.substring(0, 100)}...`);
        }
      }
    } catch (error) {
      console.log(`   ❌ FAIL - Network error: ${error.message}`);
    }
    
    console.log('');
  }

  console.log('🎯 Test Results:');
  console.log(`   Passed: ${passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    console.log('   🎉 ALL TESTS PASSED! Fix is working correctly.');
    console.log('   ✅ No more 400 errors for basic filtering');
    console.log('   ✅ Entity-specific filters properly disabled');
    console.log('   ✅ Backend behavior confirmed as expected');
  } else {
    console.log('   ⚠️  Some tests failed. Please review the results above.');
  }
}

// Run the tests
testFix().catch(console.error);
