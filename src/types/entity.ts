export interface EntityType {
  id: string;
  name: string;
  slug: string;
  description: string;
  iconUrl?: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface Category {
  id: string;
  name: string;
  slug: string;
  description: string;
  iconUrl?: string | null;
  parentCategoryId?: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface Tag {
  id: string;
  name: string;
  slug: string;
}

export interface Feature {
  id: string;
  name: string;
  slug: string;
  description: string;
  iconUrl?: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface SocialLinks {
  twitter?: string;
  linkedin?: string;
  github?: string;
  youtube?: string;
  facebook?: string;
  instagram?: string;
  discord?: string;
  [key: string]: string | undefined;
}

export interface Submitter {
  id: string;
  email: string;
  created_at: string;
  last_sign_in_at?: string | null;
  user_metadata: {
    username?: string | null;
    display_name?: string | null;
    profile_picture_url?: string | null;
    internal_user_id?: string;
  };
}

// Updated details interface based on actual API response
export interface EntityDetails {
  entityId: string;
  programmingLanguages?: string[] | null;
  frameworks?: string[] | null;
  libraries?: string[] | null;
  integrations?: string[] | null;
  keyFeatures?: string[];
  useCases?: string[];
  targetAudience?: string[];
  learningCurve?: 'LOW' | 'MEDIUM' | 'HIGH' | null;
  deploymentOptions?: string[] | null;
  supportedOs?: string[] | null;
  mobileSupport?: boolean;
  apiAccess?: boolean;
  customizationLevel?: string | null;
  trialAvailable?: boolean;
  demoAvailable?: boolean;
  openSource?: boolean;
  supportChannels?: string[];
  hasFreeTier?: boolean;
  pricingModel?: 'FREE' | 'FREEMIUM' | 'PAID' | 'SUBSCRIPTION' | 'ONE_TIME' | null;
  priceRange?: 'FREE' | 'LOW' | 'MEDIUM' | 'HIGH' | 'ENTERPRISE' | null;
  pricingDetails?: string | null;
  pricingUrl?: string | null;
  supportEmail?: string | null;
  hasLiveChat?: boolean | null;
  communityUrl?: string | null;
  [key: string]: unknown; // Allow other dynamic fields
}

// Legacy detail interfaces for backward compatibility
export interface ToolDetails {
  technical_level?: 'Beginner' | 'Intermediate' | 'Advanced' | 'Expert' | string;
  key_features?: string[];
  integrations?: string[];
  use_cases?: string[];
  pricing_model?: 'Free' | 'Freemium' | 'Paid' | 'Subscription' | 'One-time Purchase' | string;
  api_available?: boolean;
  self_hosted_option?: boolean;
  [key: string]: unknown;
}

export interface CourseDetails {
  instructor_name?: string;
  duration_text?: string;
  skill_level?: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | string; // Updated field name and values
  certificate_available?: boolean;
  prerequisites?: string[];
  learning_outcomes?: string[];
  language?: string;
  syllabus_url?: string; // New field
  enrollment_count?: number; // New field
  [key: string]: unknown;
}

export interface AgencyDetails {
  services_offered?: string[];
  portfolio_url?: string;
  team_size?: number | string;
  specializations?: string[];
  contact_email?: string;
  region_served?: string;
  location_summary?: string; // New field
  pricing_info?: string; // New field
  industry_focus?: string[]; // New field
  target_client_size?: string[]; // New field
  [key: string]: unknown;
}

export interface ContentCreatorDetails {
  platform?: 'YouTube' | 'TikTok' | 'Twitch' | 'Instagram' | 'Blog' | 'Podcast' | string;
  platform_url?: string;
  subscriber_count?: string;
  content_focus?: string[];
  collaboration_email?: string;
  sample_work_links?: string[];
  [key: string]: unknown;
}

export interface CommunityDetails {
  platform_name?: string;
  platform_url?: string;
  member_count?: string;
  main_topics?: string[];
  moderator_info?: string;
  entry_requirements?: string;
  [key: string]: unknown;
}

export interface NewsletterDetails {
  frequency?: string; // Updated field name
  target_audience?: string;
  archive_url?: string;
  subscribe_url?: string; // Updated field name
  author_name?: string;
  subscriber_count?: number;
  main_topics?: string[]; // Updated field name
  [key: string]: unknown;
}

// New entity detail interfaces for enhanced forms
export interface SoftwareDetails {
  current_version?: string;
  license_type?: string;
  community_url?: string;
  has_free_tier?: boolean;
  has_live_chat?: boolean;
  price_range?: 'FREE' | 'LOW' | 'MEDIUM' | 'HIGH' | 'ENTERPRISE';
  pricing_details?: string;
  pricing_model?: 'FREE' | 'FREEMIUM' | 'PAID' | 'SUBSCRIPTION' | 'ONE_TIME';
  pricing_url?: string;
  support_email?: string;
  api_access?: boolean;
  customization_level?: string;
  demo_available?: boolean;
  deployment_options?: string[];
  frameworks?: string[];
  has_api?: boolean;
  key_features?: string[];
  libraries?: string[];
  mobile_support?: boolean;
  open_source?: boolean;
  support_channels?: string[];
  supported_os?: string[];
  target_audience?: string[];
  trial_available?: boolean;
  integrations?: string[];
  platform_compatibility?: string[];
  programming_languages?: string[];
  use_cases?: string[];
  [key: string]: unknown;
}

export interface ModelDetails {
  model_architecture?: string;
  training_dataset?: string;
  license?: string;
  performance_metrics?: Record<string, any>;
  input_data_types?: string[];
  output_data_types?: string[];
  frameworks?: string[];
  libraries?: string[];
  deployment_options?: string[];
  use_cases?: string[];
  target_audience?: string[];
  [key: string]: unknown;
}

export interface DatasetDetails {
  license?: string;
  size_in_bytes?: number;
  format?: string;
  source_url?: string;
  collection_method?: string;
  update_frequency?: string;
  access_notes?: string;
  description?: string;
  [key: string]: unknown;
}

export interface PlatformDetails {
  platform_type?: string;
  documentation_url?: string;
  community_url?: string;
  pricing_model?: 'FREE' | 'FREEMIUM' | 'PAID' | 'SUBSCRIPTION' | 'ONE_TIME';
  price_range?: 'FREE' | 'LOW' | 'MEDIUM' | 'HIGH' | 'ENTERPRISE';
  has_free_tier?: boolean;
  has_api?: boolean;
  has_live_chat?: boolean;
  api_access?: boolean;
  demo_available?: boolean;
  trial_available?: boolean;
  mobile_support?: boolean;
  customization_level?: string;
  pricing_url?: string;
  support_email?: string;
  pricing_details?: string;
  deployment_options?: string[];
  target_audience?: string[];
  supported_os?: string[];
  integrations?: string[];
  key_services?: string[];
  use_cases?: string[];
  [key: string]: unknown;
}

export interface Entity {
  id: string;
  name: string;
  slug: string;
  description: string;
  shortDescription?: string;
  websiteUrl: string;
  logoUrl: string | null;
  documentationUrl?: string | null;
  contactUrl?: string | null;
  privacyPolicyUrl?: string | null;
  foundedYear?: number | null;
  entityType: EntityType;
  categories: Category[];
  tags: Tag[];
  features: Feature[];
  avgRating: number;
  reviewCount: number;
  saveCount: number;
  status: string; // e.g., 'ACTIVE', 'PENDING'
  socialLinks?: SocialLinks | null;
  submitter?: Submitter;
  legacyId?: string | null;
  createdAt: string;
  updatedAt: string;
  metaTitle?: string;
  metaDescription?: string;
  scrapedReviewSentimentLabel?: string | null;
  scrapedReviewSentimentScore?: number | null;
  scrapedReviewCount?: number | null;
  employeeCountRange?: string | null;
  fundingStage?: string | null;
  locationSummary?: string | null;
  refLink?: string;
  affiliateStatus?: string;
  hasFreeTier?: boolean;
  details?: EntityDetails;
}

export interface PaginationMeta {
  total: number;
  page: number;
  limit: number;
  totalItems: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface PaginatedEntities {
  data: Entity[];
  meta: PaginationMeta;
}

// Interface for query parameters for fetching entities
export interface GetEntitiesParams {
  page?: number;
  limit?: number;
  searchTerm?: string;
  categoryIds?: string[];
  tagIds?: string[];
  entityTypeIds?: string[];
  entityTypeId?: string; // Single entity type filter
  featureIds?: string[];
  sortBy?: 'createdAt' | 'updatedAt' | 'name' | 'reviewCount' | 'averageRating' | 'saveCount' | 'viewCount' | 'popularity';
  sortOrder?: 'asc' | 'desc';
  status?: string;
  submitterId?: string;

  // Date filters
  createdAtFrom?: string;
  createdAtTo?: string;

  // Boolean filters
  hasFreeTier?: boolean;
  apiAccess?: boolean;

  // Array filters
  employeeCountRanges?: string[];
  fundingStages?: string[];
  pricingModels?: string[];
  priceRanges?: string[];
  integrations?: string[];
  platforms?: string[];
  targetAudience?: string[];

  // Location search
  locationSearch?: string;

  // Rating filters
  rating_min?: number;
  rating_max?: number;

  // Review count filters
  review_count_min?: number;
  review_count_max?: number;

  // Affiliate filters
  affiliate_status?: 'NONE' | 'APPLIED' | 'APPROVED' | 'REJECTED';
  has_affiliate_link?: boolean;

  // MISSING CRITICAL FILTERS - Platform & Integration
  integrations?: string[];
  platforms?: string[];
  targetAudience?: string[];

  // Status & Moderation filters
  status?: 'PENDING' | 'ACTIVE' | 'REJECTED' | 'INACTIVE' | 'ARCHIVED' | 'NEEDS_REVISION';
  submitterId?: string;

  // Entity-specific filters (flat structure for backend compatibility)
  // Tool/AI Tool filters
  technical_levels?: string[];
  learning_curves?: string[];
  pricing_models?: string[];
  price_ranges?: string[];
  has_api?: boolean;
  has_free_tier?: boolean;
  open_source?: boolean;
  mobile_support?: boolean;
  demo_available?: boolean;
  deployment_options?: string[];
  support_channels?: string[];
  has_live_chat?: boolean;

  // Course filters
  skill_levels?: string[];
  certificate_available?: boolean;
  instructor_name?: string;
  duration_text?: string;
  enrollment_min?: number;
  enrollment_max?: number;
  prerequisites?: string;

  // Job filters
  employment_types?: string[];
  experience_levels?: string[];
  salary_min?: number;
  salary_max?: number;
  location_types?: string[];
  remote_work?: boolean;
  benefits?: string[];
  company_size?: string;

  // Hardware filters
  hardware_types?: string[];
  manufacturers?: string[];
  release_date_from?: string;
  release_date_to?: string;
  price_min?: number;
  price_max?: number;
  specifications_search?: string;
  has_datasheet?: boolean;
  memory_search?: string;
  processor_search?: string;

  // Event filters
  event_types?: string[];
  start_date_from?: string;
  start_date_to?: string;
  end_date_from?: string;
  end_date_to?: string;
  is_online?: boolean;
  location?: string;
  price_text?: string;
  registration_required?: boolean;
  has_registration_url?: boolean;
  speakers_search?: string;
  target_audience_search?: string;

  // Entity-specific filters (JSON object sent to backend)
  entity_type_filters?: Record<string, any>;
}

// Interface for creating a new entity (mirrors backend CreateEntityDto)
export interface CreateEntityDto {
  // Core entity information
  name: string;
  short_description?: string;
  description: string;
  website_url: string;
  logo_url?: string;
  documentation_url?: string;
  contact_url?: string;
  privacy_policy_url?: string;
  founded_year?: number;

  // Entity type and categorization
  entity_type_id: string;
  category_ids?: string[];
  tag_ids?: string[];
  feature_ids?: string[];

  // Social links
  social_links?: SocialLinks;

  // SEO metadata
  meta_title?: string;
  meta_description?: string;

  // Company/organization details
  employee_count_range?: string;
  funding_stage?: string;
  location_summary?: string;

  // Referral and affiliate
  ref_link?: string;
  affiliate_status?: string;

  // Type-specific details (dynamic based on entity type)
  details?: EntityDetails | ToolDetails | CourseDetails | AgencyDetails | ContentCreatorDetails | CommunityDetails | NewsletterDetails;

  // Enhanced entity detail types
  tool_details?: ToolDetails;
  course_details?: CourseDetails;
  agency_details?: AgencyDetails;
  newsletter_details?: NewsletterDetails;
  software_details?: SoftwareDetails;
  model_details?: ModelDetails;
  dataset_details?: DatasetDetails;
  platform_details?: PlatformDetails;
  community_details?: CommunityDetails;
  content_creator_details?: ContentCreatorDetails;
}